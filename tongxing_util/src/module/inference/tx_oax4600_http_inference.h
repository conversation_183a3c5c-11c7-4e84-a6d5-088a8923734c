#ifndef _TX_OAX4600_HTTP_INFERENCE_H_
#define _TX_OAX4600_HTTP_INFERENCE_H_
#include <fstream>
#include <memory>
#include "cc_module.h"
#include "curl/curl.h"
#include "inference_result.pb.h"
#include "cc_numarray_tool.h"

namespace tongxing {

// 回调函数，用于将服务器的响应数据写入到 std::string
size_t WriteCallback(void* contents, size_t size, size_t nmemb, void* userp) {
    ((std::string*)userp)->append((char*)contents, size * nmemb);
    return size * nmemb;
}

class FileUploader {
  public:
    FileUploader(const std::string& base_url_image, const std::string& base_url_tar)
            : base_url_image(base_url_image), base_url_tar(base_url_tar) {}

    void upload_image_data(const std::vector<std::shared_ptr<NumArray>>& in,
                           std::vector<std::shared_ptr<NumArray>>& outputs) {
        auto word_size = in[0]->word_size;
        auto batch_size = in[0]->shape[0];
        auto channel = in[0]->shape[1];
        auto height = in[0]->shape[2];
        auto width = in[0]->shape[3];

        // int output_count = 0;

        int data_length = batch_size * channel * height * width;

        std::vector<unsigned char> image_data;
        image_data.reserve(batch_size * channel * height * width);
        image_data.assign(in[0]->data, in[0]->data + data_length);
        // dms_frame.set_image_data(image_data.data(), image_data.size());
        // 初始化 cURL
        CURL* curl = curl_easy_init();
        if (curl) {
            CURLcode res;
            struct curl_slist* headers = nullptr;
            headers = curl_slist_append(headers, "Content-Type: image/jpeg");

            // printf("url:%s\n", base_url_image.c_str());
            // printf("input shape [%d,%d,%d,%d]\n", batch_size, channel, height, width);

            curl_easy_setopt(curl, CURLOPT_URL, base_url_image.c_str());
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS,
                             reinterpret_cast<const char*>(image_data.data()));
            curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, image_data.size());
            curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

            // 使用回调函数来捕获响应数据
            std::string response_data;
            curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
            curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response_data);

            // 记录开始时间
            auto start = std::chrono::high_resolution_clock::now();

            // 执行请求
            res = curl_easy_perform(curl);

            // 记录结束时间
            auto end = std::chrono::high_resolution_clock::now();
            std::chrono::duration<double, std::milli> elapsed = end - start;
            // std::cout << "Image Upload Elapsed Time (milliseconds): " << elapsed.count()
            //           << std::endl;

            // 检查请求结果
            if (res == CURLE_OK) {
                long http_code = 0;
                curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);
                if (http_code == 200) {
                    // 假设成功获取到 Protobuf 响应数据
                    // std::string protobuf_data;  // 假设将服务器响应的 Protobuf 数据读取到此变量中

                    // 解析 Protobuf 数据
                    InferenceResult inference_result;
                    int i = 0;
                    if (inference_result.ParseFromString(response_data)) {
                        for (auto& tensor : inference_result.tensors()) {
                            // std::cout << "Tensor shape: " << tensor.shape() << std::endl;
                            // std::cout << "Tensor data: " << tensor.data() << std::endl;

                            int32_t channel2 = tensor.shape()[1];
                            int32_t height2 = tensor.shape()[2];
                            int32_t width2 = tensor.shape()[3];
                            // printf("output%d shape [%d,%d,%d,%d]\n", i, 1, channel2, height2,
                            //        width2);
                            std::shared_ptr<NumArray> output = 
                                creat_numarray({1, channel2, height2, width2}, NumArray::DataType::FLOAT32);
                            i++;

                            memcpy(output->data,tensor.data().data(), (channel2 * height2 * width2)*sizeof(float));
                            outputs.push_back(output);
                        }
                    } else {
                        std::cerr << "Failed to parse Protobuf data." << std::endl;
                    }
                } else {
                    std::cerr << "Image Request failed with status code " << http_code << std::endl;
                }
            } else {
                std::cerr << "cURL error: " << curl_easy_strerror(res) << std::endl;
            }

            // 清理
            curl_slist_free_all(headers);
            curl_easy_cleanup(curl);
        }
    }

    void upload_tar(const std::string& tar_path) {
        // 检查 TAR 文件路径
        // if (tar_path.substr(tar_path.find_last_of(".") + 1) != "tar") {
        //     throw std::runtime_error("Provided file is not a TAR file.");
        // }

        // 读取 TAR 文件
        std::ifstream tar_file(tar_path, std::ios::binary);
        if (!tar_file) {
            throw std::runtime_error("Failed to open TAR file.");
        }
        std::string tar_data((std::istreambuf_iterator<char>(tar_file)),
                             std::istreambuf_iterator<char>());

        // 初始化 cURL
        CURL* curl = curl_easy_init();
        if (curl) {
            CURLcode res;
            struct curl_slist* headers = nullptr;
            headers = curl_slist_append(headers, "Content-Type: application/x-tar");

            curl_easy_setopt(curl, CURLOPT_URL, base_url_tar.c_str());
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, tar_data.c_str());
            curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, tar_data.size());
            curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

            // 记录开始时间
            auto start = std::chrono::high_resolution_clock::now();

            // 执行请求
            res = curl_easy_perform(curl);

            // 记录结束时间
            auto end = std::chrono::high_resolution_clock::now();
            std::chrono::duration<double, std::milli> elapsed = end - start;
            std::cout << "TAR Upload Elapsed Time (milliseconds): " << elapsed.count() << std::endl;

            // 检查请求结果
            if (res == CURLE_OK) {
                long http_code = 0;
                curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);
                if (http_code == 200) {
                    std::cout << "TAR file uploaded successfully." << std::endl;
                } else {
                    std::cerr << "TAR Request failed with status code " << http_code << std::endl;
                }
            } else {
                std::cerr << "cURL error: " << curl_easy_strerror(res) << std::endl;
            }

            // 清理
            curl_slist_free_all(headers);
            curl_easy_cleanup(curl);
        }
    }

  private:
    std::string base_url_image;
    std::string base_url_tar;
};

class TXOAX4600HttpInference : public CcModule {
  public:
    int init(const Json::Value& root);
    const std::vector<std::shared_ptr<CcTensor<float>>>& getInputTensor();
    ~TXOAX4600HttpInference();
    int setInput(const std::vector<std::shared_ptr<NumArray>>& in);
    int execute();
    size_t getOutputNum();
    std::shared_ptr<NumArray> getOutput(int index);

  private:
    Json::Value root_;

    std::vector<std::shared_ptr<NumArray>> input_;
    std::vector<std::shared_ptr<NumArray>> output_;
    std::string http_url_;
    std::string model_url_;

    std::shared_ptr<FileUploader> uploader;
};

}  // namespace tongxing

#endif
